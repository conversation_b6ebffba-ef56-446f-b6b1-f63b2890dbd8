import 'package:get/get.dart';
import 'package:likewallet/service/getStorage.dart';

class DrawerOwnController extends GetxController {

  RxBool activeTouchID = false.obs;
  RxBool saveSlip = false.obs;
  RxBool notify = false.obs;

  Future<void> checkFirst() async {
    // ตรวจสอบว่ามีการตั้งค่า Touch ID หรือไม่
    activeTouchID.value = await Storage.get<bool>(StorageKeys.activeTouchID) ?? false;

    // ตรวจสอบว่ามีการบันทึกสลิปหรือไม่
    saveSlip.value = await Storage.get<bool>(StorageKeys.saveSlipAllow) ?? false;

    // ตรวจสอบว่ามีการแจ้งเตือนหรือไม่
    notify.value = await Storage.get<bool>(StorageKeys.notifyAllow) ?? false;
  }

  void toggleTouchID() {
    activeTouchID.value = !activeTouchID.value;
    Storage.save(StorageKeys.activeTouchID, activeTouchID.value);
  }

  void toggleSaveSlip() {
    saveSlip.value = !saveSlip.value;
    Storage.save(StorageKeys.saveSlipAllow, saveSlip.value);
  }

  void toggleNotify() {
    notify.value = !notify.value;
    Storage.save(StorageKeys.notifyAllow, notify.value);
  }
}