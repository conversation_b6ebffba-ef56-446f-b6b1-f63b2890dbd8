import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:likewallet/controller/profile/profileController.dart';
import 'package:likewallet/model/historyModel/transactionModel.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/service/globalConfig.dart';
import 'package:likewallet/service/httpRequest.dart';

class HistoryController extends GetxController {
  RxInt tabSelect = 1.obs;
  final fireStore = FirebaseFirestore.instance;
  ProfileController profileCtrl = Get.find<ProfileController>();

  RxList<TransactionModel> all = <TransactionModel>[].obs;
  RxList<TransactionModel> sent = <TransactionModel>[].obs;
  RxList<TransactionModel> receive = <TransactionModel>[].obs;
  RxList<TransactionModel> reward = <TransactionModel>[].obs;

  RxString selectedFilter = 'All'.obs;
  RxString previousFilter = 'All'.obs;

  // Add loading and error states
  RxBool isLoading = false.obs;
  RxBool hasError = false.obs;
  RxString errorMessage = ''.obs;

  String account = Storage.get(StorageKeys.addressETH) ?? "";

  // @override
  // void onInit() {
  //   super.onInit();
  //   // Automatically load history when controller is initialized
  //   getHistory();
  // }

  Future<void> changeTabSelection(int tab) async {
    if (tabSelect.value != tab) {

      await Future.delayed(Duration(milliseconds: 150));
      tabSelect.value = tab;

    }
  }

  Future<void> getHistory() async {
    try {
      isLoading.value = true;
      hasError.value = false;
      errorMessage.value = '';

      print("getHistory");
      var address = Storage.get(StorageKeys.addressETH) ?? "";
      var token = await profileCtrl.getTokenFirebase();

      if (address.isEmpty) {
        throw Exception('User address not found');
      }

      Map<String, dynamic> body = {
        "_token": token,
        "address": address,
      };

      // print("Address: $address");
      // print("Request body: $body");

      // เรียก API เพื่อดึงข้อมูลประวัติการทำธุรกรรม
      var response = await AppApi.post('${AppEnv.apiUrl}/getHistoryNew', body);

      print("API Response: ");
      print(response['result'][0]);
      print(response['result'][1]);
      print(response['result'][2]);
      print(response['result'][3]);

      if (response != null && response['statusCode'] == 200) {
        List<TransactionModel> parsed = [];

        if (response['result'] != null && response['result'] is List) {
          for (var item in response['result']) {
            try {
              var model = TransactionModel.fromJson(item as Map<String, dynamic>);
              parsed.add(model);
            } catch (e) {
              print("Error parsing transaction item: $e");
            }
          }
        }

        // Update observable lists
        // all.assignAll(parsed);
        // sent.assignAll(parsed.where((e) => e.title?.toLowerCase() == 'sent'));
        // receive.assignAll(parsed.where((e) => e.title?.toLowerCase() == 'recv' && e.to != 'Reward'));
        // reward.assignAll(parsed.where((e) => e.title?.toLowerCase() == 'recv' && e.to == 'Reward'));
        all.assignAll(parsed);

        sent.assignAll(parsed.where(
              (e) => e.type == 'transaction'
              ? (e.accountNumber == account ? true : false)
              : true,
        ));

        receive.assignAll(parsed.where(
              (e) => e.type == 'transaction'
              ? (e.accountNumber != account ? true : false)
              : false,
        ));

        reward.assignAll(parsed.where(
              (e) => e.type == 'transaction'
              ? (e.accountNumber != account
              ? (e.to == 'Reward' ? true : false)
              : false)
              : false,
        ));


        print("Parsed ${parsed.length} transactions");
        print("All: ${all.length}, Sent: ${sent.length}, Receive: ${receive.length}, Reward: ${reward.length}");

        print("All: ${all[0]}");
      } else {
        String errorMsg = response?['message'] ?? 'Unknown error occurred';
        print("API Error: $errorMsg");
        hasError.value = true;
        errorMessage.value = errorMsg;
      }

    } catch (e) {
      print("Exception in getHistory: $e");
      hasError.value = true;
      errorMessage.value = e.toString();
    } finally {
      isLoading.value = false;
      update();
    }
  }

}