import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:likewallet/controller/profile/profileController.dart';
import 'package:likewallet/controller/rewardCurrency/rewardCurrencyController.dart';
import 'package:likewallet/controller/web3/web3ToolsController.dart';
import 'package:likewallet/model/rewardModel/nextRewardModel.dart';
import 'package:likewallet/model/rewardModel/rewardModel.dart';
import 'package:likewallet/service/getStorage.dart';
import 'package:likewallet/service/globalConfig.dart';
import 'package:likewallet/service/httpRequest.dart';


enum ClaimStatus { INACTIVE, ACTIVE }

class RewardController extends GetxController {

  final profileCtrl = Get.find<ProfileController>();
  final web3Ctrl = Get.put(Web3toolsController());

  // Formatters
  final NumberFormat f = NumberFormat("###,###", "en_US");
  final NumberFormat fdecimal = NumberFormat("#,##0.00000000000", "en_US");

  // Reward data
  RxDouble rewards = 0.0.obs;
  RxDouble rewardsRecieve = 0.0.obs;
  RxDouble rewardsShow = 0.0.obs;
  RxDouble nextRewards = 0.0.obs;
  RxDouble totalLocked = 0.0.obs;
  RxDouble Income = 0.0.obs;
  RxDouble upcomingRewards = 0.0.obs;
  RxString rewardTime = "00:00:00".obs;
  RxString image = "".obs;
  Timer? _rewardTimer;

  RxBool youLock = false.obs;


  // Claim status
  Rx<ClaimStatus> isClaim = ClaimStatus.INACTIVE.obs;
  RxBool claimable = false.obs;

  // Button states
  RxInt buttonClaim = 0.obs;
  RxInt buttonClaimAndLock = 0.obs;

  // Currency types
  RxBool isLIKE = true.obs;
  RxBool isBTC = false.obs;
  RxBool isGOLD = false.obs;

  // Income data
  RxDouble income = 0.0.obs;

  // Loading state
  RxBool isLoading = false.obs;

  // Animation controller for loading animation
  late AnimationController animationController;

  TextEditingController addressText = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    getReward();
    getButtonStatus();
    getNewReward();
    startRewardTimer();
  }

  @override
  void onClose() {
    _rewardTimer?.cancel();
    super.onClose();
  }

  // Calculate the next 11:00 AM (either today or tomorrow)
  DateTime getNextRewardTime() {
    final now = DateTime.now();
    final eleventh = DateTime(now.year, now.month, now.day, 11, 0, 0);

    // If it's already past 11:00 AM, set for tomorrow
    if (now.isAfter(eleventh)) {
      return eleventh.add(Duration(days: 1));
    }

    return eleventh;
  }

  // Start timer to update countdown to next reward
  void startRewardTimer() {
    // Cancel existing timer if any
    _rewardTimer?.cancel();

    // Update immediately
    updateRewardTimeRemaining();

    // Set up periodic timer to update every second
    _rewardTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      updateRewardTimeRemaining();
    });
  }

  // Update the time remaining until next reward
  void updateRewardTimeRemaining() {
    final now = DateTime.now();
    final nextReward = getNextRewardTime();
    final difference = nextReward.difference(now);

    // Format the remaining time as HH:MM:SS
    final hours = difference.inHours.toString().padLeft(2, '0');
    final minutes = (difference.inMinutes % 60).toString().padLeft(2, '0');
    final seconds = (difference.inSeconds % 60).toString().padLeft(2, '0');

    rewardTime.value = "$hours:$minutes:$seconds";
    update();
  }

  getReward() async {
    await FirebaseFirestore.instance
        .collection('reward_time')
        .doc('oBAkQv2YMT0lfg9ku2Fw')
        .get()
        .then((DocumentSnapshot<Map<String, dynamic>> ds) {

        rewardTime = ds.data()!['time'];
      update();
      print("เวลาเเจก :" + ds.data()!['time']);
    });
    FirebaseFirestore.instance
        .collection('imageURL')
        .doc('page')
        .collection('reward_page')
        .doc('reward_image_head')
        .get()
        .then(
          (value) {
        image = value.data()!['image'];
        update();
          },
    );
  }

  getButtonStatus() async {
    try {
      // print('getButtonStatus' + context.read(tierLevel).state);

      final res = await FirebaseFirestore.instance
          .collection('tierController')
          .doc('controller')
          .collection(profileCtrl.tierLevel)
          .doc('button')
          .get();
      final data = ButtonStatus.fromJson(res.data()!);

        claimable.value = data.claim;
        // claimLockable = data.claimLock;
      update();
    } catch (e) {
      print(e);
    }
  }

  Future<void> claimRewards() async {
    try{
      var tx = await web3Ctrl.ClaimRewards();
      if(tx == "error"){
        buttonClaim.value = 0;

      }else{
        buttonClaim.value = 2;
        rewards.value = 0.0;

        // Trigger internal backend process after successful reward claim
        await _processRewardClaim();
      }
    }catch(e){
      buttonClaim.value = 0;
      print("Error: $e");
    }
    update();
  }

  /// Private function to trigger internal backend process after successful reward claim
  /// Only calls the backend API if rewardCurrency is not equal to "like"
  Future<void> _processRewardClaim() async {
    try {
      // Get the current reward currency from RewardCurrencyController
      final rewardCurrencyController = Get.find<RewardCurrencyController>();
      final currentRewardCurrency = rewardCurrencyController.selectedRewardCurrency.value;

      print('Current reward currency: $currentRewardCurrency');

      // Only call backend API if reward currency is not "like"
      if (currentRewardCurrency != 'like') {
        print('Triggering backend reward process for currency: $currentRewardCurrency');

        // Prepare backend process data
        final processData = {
          'userId': profileCtrl.phoneNumber.value,
          'rewardCurrency': currentRewardCurrency,
          'claimedAmount': rewards.value,
          'timestamp': DateTime.now().toIso8601String(),
          'transactionHash': 'tx_hash_placeholder', // You can replace this with actual tx hash if available
        };

        // Call the internal backend process API
        await _callBackendRewardProcess(processData);

      } else {
        print('Reward currency is "like", skipping backend process');
      }

    } catch (e) {
      print('Error in _processRewardClaim: $e');
      // Don't throw error to avoid affecting the main claim flow
    }
  }

  /// Private function to call the internal backend reward process API
  /// Uses mock backend URL for now: https://mock.api/internal/reward-process
  Future<void> _callBackendRewardProcess(Map<String, dynamic> data) async {
    try {
      const String mockBackendUrl = 'https://mock.api/internal/reward-process';

      print('Calling backend reward process API: $mockBackendUrl');
      print('Process data: $data');

      // Make POST request to the backend API
      final response = await AppApi.post(mockBackendUrl, data);

      if (response != null) {
        print('Backend reward process API response: $response');

        // Handle successful response
        if (response['statusCode'] == 200 || response['success'] == true) {
          print('Backend reward process completed successfully');
        } else {
          print('Backend reward process API returned error: ${response['message'] ?? 'Unknown error'}');
        }
      } else {
        print('Backend reward process API returned null response');
      }

    } catch (e) {
      print('Error calling backend reward process API: $e');
      // Log error but don't throw to avoid affecting main claim flow
    }
  }

  Future<void> getNewReward() async {
    try {
      print("getNewReward");
      isLoading.value = true;

      // Get next rewards data from API
      var response = await AppApi.post('${AppEnv.apiUrl}/nextRewards', {'application': 'LIKEWALLET'});

      print(response);
      if (response['statusCode'] == 200) {
        final body2 = NextRewards.fromJson(response["result"]);
        final currentTime = DateTime.now();

        // Calculate time difference (convert milliseconds properly)
        final roundTimeMs = double.parse(body2.round_time);
        final currentTimeMs = currentTime.millisecondsSinceEpoch;
        final timeZoneOffsetMs = 25200000; // 7 hours in milliseconds

        final diffTime = ((roundTimeMs / 1000) - (currentTimeMs + timeZoneOffsetMs) / 1000);

        // Parse reward data
        totalLocked.value = double.parse(body2.total_locked);
        nextRewards.value = double.parse(body2.rewards);

        // Calculate income (avoid division by zero)
        if (totalLocked.value > 0) {
          Income.value = (nextRewards.value / totalLocked.value) * 365 * 100;
        } else {
          Income.value = 0.0;
        }

        // Get user's ETH address
        final addressETH = Storage.get<String>(StorageKeys.addressETH);
        if (addressETH == null || addressETH.isEmpty) {
          isLoading.value = false;
          return;
        }

        // Check if user has locked tokens
        final unlockData = await web3Ctrl.getUnlockDate(address: addressETH);
        youLock.value = unlockData[3] == '1';

        // Get deposit time
        final timeLock = await web3Ctrl.getDepositTime(address: addressETH);
        final createdTimeSeconds = int.parse(body2.created_time) ~/ 1000;
        final timeLockInt = int.parse(timeLock);

        // Calculate upcoming rewards based on lock status
        if (timeLockInt == 0) {
          // User has no locked tokens
          upcomingRewards.value = 0.0;
        } else {
          // User has locked tokens, calculate rewards
          final lock = await web3Ctrl.getBalanceLock(address: addressETH);

          if (totalLocked.value > 0) {
            upcomingRewards.value = (lock / totalLocked.value) * nextRewards.value;
          } else {
            upcomingRewards.value = 0.0;
          }
        }

        // Check claim status
        final lastTime = await web3Ctrl.checkClaim(address: addressETH);
        final roundAirdrop = await web3Ctrl.getRound();

        print("lastTime $lastTime");
        if (int.parse(lastTime) >= int.parse(roundAirdrop)) {
          // User has already claimed for this round
          final dataBack = await web3Ctrl.isWithdraw(address: addressETH);

          print("dataBack $dataBack");

          if (dataBack == 1) {
            isClaim.value = ClaimStatus.INACTIVE;
            rewards.value = 0.0;
          } else {
            isClaim.value = ClaimStatus.ACTIVE;
          }
        } else {
          // User has not claimed for this round
          final dataBack = await web3Ctrl.isWithdraw(address: addressETH);

          print("dataBack $dataBack");
          if (dataBack == 1) {
            isClaim.value = ClaimStatus.INACTIVE;
            rewards.value = 0.0;
            rewardsShow.value = 0.0;

            getRewardNoLike();

          } else {
            print("Claim status");
            isClaim.value = ClaimStatus.ACTIVE;
            final balance = await web3Ctrl.checkRewards();

            if (balance is num) {
              rewards.value = balance.toDouble();
              rewardsRecieve.value = balance.toDouble();
              rewardsShow.value = balance.toDouble();

              getRewardNoLike();
            } else {
              rewards.value = 0.0;
              rewardsShow.value = 0.0;
            }
          }
        }
      } else {
        print("API Error: ${response['message'] ?? 'Unknown error'}");
      }
    } catch (e) {
      print("Error in getNextReward: $e");
    } finally {
      isLoading.value = false;
      update();
    }
  }

  Future<void> getRewardNoLike() async {
    try{

      final RewardCurrencyController rewardCurrencyCtrl = Get.isRegistered<RewardCurrencyController>()
          ? Get.find<RewardCurrencyController>()
          : Get.put(RewardCurrencyController());

      var selecetedCurrency = rewardCurrencyCtrl.selectedRewardCurrency.value;

      if(selecetedCurrency == "like"){
        print("Selected currency is LIKE, no need to call API");
        return;
      }

      // var rewardCurrency = ;
      Map data = {
        "like_amount" : rewards.value,
        "currency" : "btc"
      };

      var response = await AppApi.post('https://n8n-ags.agilesoftgroup.com/webhook/likewallet/calcurate2crypto', data);

      if(response != null){

        // Update rewards based on selected currency
        if (selecetedCurrency == "btc") {
          rewardsShow.value = response["amount"];
        } else if (selecetedCurrency == "gold") {
          rewardsShow.value = response["amount"];
        } else {
          print("Unsupported currency: $selecetedCurrency");
        }
      } else {
        print("API Error: ${response['message'] ?? 'Unknown error'}");
      }

    }catch(e){
      print("Error in getRewardNoLike: $e");

    }
  }

}
