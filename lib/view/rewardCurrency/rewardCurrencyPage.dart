import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:likewallet/controller/rewardCurrency/rewardCurrencyController.dart';

class RewardCurrencyPage extends StatelessWidget {
  const RewardCurrencyPage({super.key});

  @override
  Widget build(BuildContext context) {
    final RewardCurrencyController controller = Get.put(RewardCurrencyController());

    return Obx(() => ModalProgressHUD(
      inAsyncCall: controller.isLoading.value,
      color: Colors.black,
      opacity: 0.8,
      progressIndicator: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
          SizedBox(height: 16.h),
          Text(
            'Loading...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16.sp,
              fontFamily: 'ProximaNova',
            ),
          ),
        ],
      ),
      child: Material(
        color: const Color(0xff1e1d34),
        child: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            // Header with back button and title
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () => Navigator.of(context).pop(),
                    child: const Icon(Icons.arrow_back_ios, color: Colors.white),
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'Reward Currency',
                    style: TextStyle(
                      fontFamily: 'ProximaNova',
                      fontSize: 24.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

            // Divider
            const Divider(color: Colors.white24),

            // Currency list
            Expanded(
              child: Obx(() => ListView(
                children: controller.rewardCurrencies.map((currency) {
                  final isSelected = currency['key'] == controller.selectedRewardCurrency.value;

                  return InkWell(
                    onTap: () async {
                      // Use ModalProgressHUD version since page has ModalProgressHUD wrapper
                      await controller.saveRewardCurrencyWithModalHUD(currency['key']!);
                      // Only pop if currency was successfully changed
                      if (controller.selectedRewardCurrency.value == currency['key']) {
                        Navigator.of(context).pop();
                      }
                    },
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            currency['label']!,
                            style: TextStyle(
                              fontFamily: 'ProximaNova',
                              fontSize: 16.sp,
                              color: Colors.white,
                            ),
                          ),
                          if (isSelected)
                            const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 20,
                            ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              )),
            ),
          ],
        ),
      ),
    ),
    ));
  }
}
