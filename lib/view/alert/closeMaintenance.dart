import 'package:flutter/material.dart';
import 'package:likewallet/service/components.dart';

class CloseMaintenance extends StatefulWidget {
  CloseMaintenance({this.url, this.title, this.detail, this.detailTime});

  final String? title;
  final String? url;
  final String? detail;
  final String? detailTime;

  @override
  State<CloseMaintenance> createState() => _CloseMaintenanceState();
}

class _CloseMaintenanceState extends State<CloseMaintenance> {
  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () { return Future.value(); },
        child: Scaffold(
          body: Stack(
            alignment: Alignment.center,
            children: <Widget>[
              widget.url == ''
                  ? SpinKitFadingCircle(
                color: LikeWalletAppTheme.bule1,
                size: mediaQuery(context, 'height', 100),
              )
                  : Image.network(
                widget.url!,
                fit: BoxFit.fill,
                width: MediaQuery.of(context).size.width,
              ),
              Positioned(
                  top: mediaQuery(context, 'height', 139),
                  left: mediaQuery(context, 'width', 75),
                  child: backButton(
                    context,
                    Colors.white,
                  )),
              Positioned(
                top: mediaQuery(context, 'height', 905),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      width: mediaQuery(context, 'width', 600),
                      child: Text(
                        widget.title!,
                        style: TextStyle(
                          fontFamily: 'Proxima Nova',
                          fontSize: mediaQuery(context, 'height', 42),
                          color: const Color(0xff00c5c2),
                          letterSpacing: mediaQuery(context, 'height', 6.3),
                          fontWeight: FontWeight.w300,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Container(
                      width: mediaQuery(context, 'width', 600),
                      child: Text(
                        widget.detail!,
                        style: TextStyle(
                          fontFamily: 'Proxima Nova',
                          fontSize: mediaQuery(context, 'height', 42),
                          color: const Color(0xff00c5c2),
                          letterSpacing: mediaQuery(context, 'height', 6.3),
                          fontWeight: FontWeight.w300,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    SizedBox(
                      height: mediaQuery(context, 'height', 42),
                    ),
                    Container(
                      width: mediaQuery(context, 'width', 600),
                      child: Text(
                        widget.detailTime.toString(),
                        style: TextStyle(
                          fontFamily: 'Proxima Nova',
                          fontSize: mediaQuery(context, 'height', 42),
                          color: const Color(0xff00c5c2),
                          letterSpacing: mediaQuery(context, 'height', 6.3),
                          fontWeight: FontWeight.w300,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
    );
  }
}
