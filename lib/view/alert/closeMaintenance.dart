import 'package:flutter/material.dart';
import 'package:loading_indicator/loading_indicator.dart';
import 'package:likewallet/service/components.dart';

class CloseMaintenancePopup extends StatefulWidget {
  CloseMaintenancePopup({this.url, this.title, this.detail, this.detailTime});

  final String? title;
  final String? url;
  final String? detail;
  final String? detailTime;

  @override
  State<CloseMaintenancePopup> createState() => _CloseMaintenancePopupState();
}

class _CloseMaintenancePopupState extends State<CloseMaintenancePopup> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        alignment: Alignment.center,
        children: <Widget>[
          widget.url == ''
              ? SizedBox(
            width: mediaQuery(context, 'height', 100),
            height: mediaQuery(context, 'height', 100),
            child: LoadingIndicator(
              indicatorType: Indicator.circleStrokeSpin,
              colors: [LikeWalletAppTheme.bule1],
              strokeWidth: 3.0,
            ),
          )
              : Image.network(
            widget.url!,
            fit: BoxFit.fill,
            width: MediaQuery.of(context).size.width,
          ),
          Positioned(
              top: mediaQuery(context, 'height', 139),
              left: mediaQuery(context, 'width', 75),
              child: backButton(
                context,
                Colors.white,
              )),
          Positioned(
            top: mediaQuery(context, 'height', 905),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: mediaQuery(context, 'width', 600),
                  child: Text(
                    widget.title!,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: mediaQuery(context, 'height', 42),
                      color: const Color(0xff00c5c2),
                      letterSpacing: mediaQuery(context, 'height', 6.3),
                      fontWeight: FontWeight.w300,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Container(
                  width: mediaQuery(context, 'width', 600),
                  child: Text(
                    widget.detail!,
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: mediaQuery(context, 'height', 42),
                      color: const Color(0xff00c5c2),
                      letterSpacing: mediaQuery(context, 'height', 6.3),
                      fontWeight: FontWeight.w300,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(
                  height: mediaQuery(context, 'height', 42),
                ),
                Container(
                  width: mediaQuery(context, 'width', 600),
                  child: Text(
                    widget.detailTime.toString(),
                    style: TextStyle(
                      fontFamily: 'Proxima Nova',
                      fontSize: mediaQuery(context, 'height', 42),
                      color: const Color(0xff00c5c2),
                      letterSpacing: mediaQuery(context, 'height', 6.3),
                      fontWeight: FontWeight.w300,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
