{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "camera_avfoundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.19/", "native_build": true, "dependencies": []}, {"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.8/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": true, "dependencies": []}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.4.6/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.13.1/", "native_build": true, "dependencies": []}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-12.4.6/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "flutter_contacts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_contacts-1.1.9+2/", "native_build": true, "dependencies": []}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-4.2.1/", "native_build": true, "dependencies": []}, {"name": "fluttertoast", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/", "native_build": true, "dependencies": []}, {"name": "image_gallery_saver", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/", "native_build": true, "dependencies": []}, {"name": "image_picker_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/", "native_build": true, "dependencies": []}, {"name": "local_auth_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "lock_to_win", "path": "/Users/<USER>/Documents/GitHub/LikeWalletPharse3/lock_to_win/", "native_build": true, "dependencies": ["cloud_firestore", "firebase_auth", "firebase_core", "flutter_secure_storage", "fluttertoast"]}, {"name": "mobile_scanner", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "permission_handler_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/", "native_build": true, "dependencies": []}, {"name": "qr_code_scanner", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/", "native_build": true, "dependencies": []}, {"name": "qr_code_tools", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/qr_code_tools-0.2.0/", "native_build": true, "dependencies": []}, {"name": "restart_app", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/restart_app-1.2.1/", "native_build": true, "dependencies": []}, {"name": "scan", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scan-1.6.0/", "native_build": true, "dependencies": []}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "sqflite", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "url_launcher_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/", "native_build": true, "dependencies": []}, {"name": "webview_flutter_wkwebview", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-2.9.5/", "native_build": true, "dependencies": []}], "android": [{"name": "camera_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_android-0.10.9+11/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.8/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": true, "dependencies": []}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.4.6/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.13.1/", "native_build": true, "dependencies": []}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-12.4.6/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "flutter_contacts", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_contacts-1.1.9+2/", "native_build": true, "dependencies": []}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/", "native_build": true, "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.22/", "native_build": true, "dependencies": []}, {"name": "flutter_secure_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-4.2.1/", "native_build": true, "dependencies": []}, {"name": "fluttertoast", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/", "native_build": true, "dependencies": []}, {"name": "image_gallery_saver", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/", "native_build": true, "dependencies": []}, {"name": "image_picker_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+12/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "local_auth_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.43/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "lock_to_win", "path": "/Users/<USER>/Documents/GitHub/LikeWalletPharse3/lock_to_win/", "native_build": true, "dependencies": ["cloud_firestore", "firebase_auth", "firebase_core", "flutter_secure_storage", "fluttertoast"]}, {"name": "mobile_scanner", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.10/", "native_build": true, "dependencies": []}, {"name": "permission_handler_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.0.13/", "native_build": true, "dependencies": []}, {"name": "qr_code_scanner", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/qr_code_scanner-1.0.1/", "native_build": true, "dependencies": []}, {"name": "qr_code_tools", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/qr_code_tools-0.2.0/", "native_build": true, "dependencies": []}, {"name": "restart_app", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/restart_app-1.2.1/", "native_build": true, "dependencies": []}, {"name": "scan", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/scan-1.6.0/", "native_build": true, "dependencies": []}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.3.2/", "native_build": true, "dependencies": []}, {"name": "sqflite", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/", "native_build": true, "dependencies": []}, {"name": "url_launcher_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.9/", "native_build": true, "dependencies": []}, {"name": "webview_flutter_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-2.10.4/", "native_build": true, "dependencies": []}], "macos": [{"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.8/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": true, "dependencies": []}, {"name": "file_selector_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+2/", "native_build": true, "dependencies": []}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.4.6/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.13.1/", "native_build": true, "dependencies": []}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.6/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-12.4.6/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-17.2.4/", "native_build": true, "dependencies": []}, {"name": "image_picker_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/", "native_build": false, "dependencies": ["file_selector_macos"]}, {"name": "local_auth_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "mobile_scanner", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "sqflite", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "url_launcher_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/", "native_build": true, "dependencies": []}], "linux": [{"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": false, "dependencies": []}, {"name": "file_selector_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/", "native_build": true, "dependencies": []}, {"name": "flutter_local_notifications_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/", "native_build": false, "dependencies": []}, {"name": "flutter_secure_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-4.2.1/", "native_build": true, "dependencies": []}, {"name": "image_picker_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/", "native_build": false, "dependencies": ["file_selector_linux"]}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": false, "dependencies": []}, {"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": []}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/", "native_build": false, "dependencies": ["url_launcher_linux"]}, {"name": "shared_preferences_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/", "native_build": false, "dependencies": ["path_provider_linux"]}, {"name": "url_launcher_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/", "native_build": true, "dependencies": []}], "windows": [{"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-5.6.8/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": false, "dependencies": []}, {"name": "file_selector_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/", "native_build": true, "dependencies": []}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.5.4/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.13.1/", "native_build": true, "dependencies": []}, {"name": "firebase_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-12.4.6/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "image_picker_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/", "native_build": false, "dependencies": ["file_selector_windows"]}, {"name": "local_auth_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": false, "dependencies": []}, {"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": []}, {"name": "permission_handler_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/", "native_build": true, "dependencies": []}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/", "native_build": true, "dependencies": ["url_launcher_windows"]}, {"name": "shared_preferences_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/", "native_build": false, "dependencies": ["path_provider_windows"]}, {"name": "url_launcher_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/", "native_build": true, "dependencies": []}], "web": [{"name": "camera_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camera_web-0.3.5/", "dependencies": []}, {"name": "cloud_firestore_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-4.4.8/", "dependencies": ["firebase_core_web"]}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "dependencies": []}, {"name": "firebase_analytics_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+12/", "dependencies": ["firebase_core_web"]}, {"name": "firebase_auth_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-5.14.3/", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/", "dependencies": []}, {"name": "firebase_messaging_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.10.6/", "dependencies": ["firebase_core_web"]}, {"name": "firebase_storage_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage_web-3.10.13/", "dependencies": ["firebase_core_web"]}, {"name": "fluttertoast", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/", "dependencies": []}, {"name": "image_picker_for_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/", "dependencies": []}, {"name": "lock_to_win", "path": "/Users/<USER>/Documents/GitHub/LikeWalletPharse3/lock_to_win/", "dependencies": ["fluttertoast"]}, {"name": "mobile_scanner", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/", "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "dependencies": []}, {"name": "permission_handler_html", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/", "dependencies": []}, {"name": "restart_app", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/restart_app-1.2.1/", "dependencies": []}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/", "dependencies": ["url_launcher_web"]}, {"name": "shared_preferences_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/", "dependencies": []}, {"name": "url_launcher_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.3.3/", "dependencies": []}]}, "dependencyGraph": [{"name": "camera", "dependencies": ["camera_android", "camera_avfoundation", "camera_web", "flutter_plugin_android_lifecycle"]}, {"name": "camera_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "camera_avfoundation", "dependencies": []}, {"name": "camera_web", "dependencies": []}, {"name": "cloud_firestore", "dependencies": ["cloud_firestore_web", "firebase_core"]}, {"name": "cloud_firestore_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "device_info_plus", "dependencies": []}, {"name": "file_selector_linux", "dependencies": []}, {"name": "file_selector_macos", "dependencies": []}, {"name": "file_selector_windows", "dependencies": []}, {"name": "firebase_analytics", "dependencies": ["firebase_analytics_web", "firebase_core"]}, {"name": "firebase_analytics_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_auth", "dependencies": ["firebase_auth_web", "firebase_core"]}, {"name": "firebase_auth_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "firebase_messaging", "dependencies": ["firebase_core", "firebase_messaging_web"]}, {"name": "firebase_messaging_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_storage", "dependencies": ["firebase_core", "firebase_storage_web"]}, {"name": "firebase_storage_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "flutter_contacts", "dependencies": []}, {"name": "flutter_local_notifications", "dependencies": ["flutter_local_notifications_linux"]}, {"name": "flutter_local_notifications_linux", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "flutter_secure_storage", "dependencies": []}, {"name": "fluttertoast", "dependencies": []}, {"name": "image_gallery_saver", "dependencies": []}, {"name": "image_picker", "dependencies": ["image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_windows"]}, {"name": "image_picker_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_for_web", "dependencies": []}, {"name": "image_picker_ios", "dependencies": []}, {"name": "image_picker_linux", "dependencies": ["file_selector_linux"]}, {"name": "image_picker_macos", "dependencies": ["file_selector_macos"]}, {"name": "image_picker_windows", "dependencies": ["file_selector_windows"]}, {"name": "local_auth", "dependencies": ["local_auth_android", "local_auth_darwin", "local_auth_windows"]}, {"name": "local_auth_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "local_auth_darwin", "dependencies": []}, {"name": "local_auth_windows", "dependencies": []}, {"name": "lock_to_win", "dependencies": ["cloud_firestore", "firebase_auth", "firebase_core", "shared_preferences", "flutter_secure_storage", "fluttertoast"]}, {"name": "mobile_scanner", "dependencies": []}, {"name": "package_info_plus", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "qr_code_scanner", "dependencies": []}, {"name": "qr_code_tools", "dependencies": []}, {"name": "restart_app", "dependencies": []}, {"name": "scan", "dependencies": []}, {"name": "share_plus", "dependencies": ["url_launcher_web", "url_launcher_windows", "url_launcher_linux"]}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sqflite", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}, {"name": "webview_flutter", "dependencies": ["webview_flutter_android", "webview_flutter_wkwebview"]}, {"name": "webview_flutter_android", "dependencies": []}, {"name": "webview_flutter_wkwebview", "dependencies": []}], "date_created": "2025-07-03 10:58:43.224531", "version": "3.22.2"}